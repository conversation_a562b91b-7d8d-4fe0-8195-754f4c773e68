/* Custom Keyframe Animations */
@keyframes fadeInUp {
  from { opacity: 0; transform: translateY(30px); }
  to { opacity: 1; transform: translateY(0); }
}

@keyframes slideIn {
  from { opacity: 0; transform: translateX(-20px); }
  to { opacity: 1; transform: translateX(0); }
}

@keyframes gradient-x {
  0%, 100% { background-size: 200% 200%; background-position: left center; }
  50% { background-size: 200% 200%; background-position: right center; }
}

.animate-fadeInUp { animation: fadeInUp 0.8s ease-out forwards; }
.animate-slideIn { animation: slideIn 0.6s ease-out forwards; }
.animate-gradient-x { animation: gradient-x 3s ease infinite; }

#floating-navbar.scrolled {
  background-color: rgba(255, 255, 255, 0.95);
  transform: translateY(-120%) scale(0.98);
  box-shadow: 0 25px 50px -12px rgba(0, 0, 0, 0.25);
}

/* Mobile menu open state */
#hamburger-button.menu-open .top-line {
  transform: rotate(45deg) translateY(0px);
  width: 1.5rem; /* 24px */
  top: 0.75rem; /* 12px */
}
#hamburger-button.menu-open .middle-line {
  opacity: 0;
  transform: scale(0);
}
#hamburger-button.menu-open .bottom-line {
  transform: rotate(-45deg) translateY(0px);
  width: 1.5rem; /* 24px */
  top: 0.75rem; /* 12px */
}

/* Desktop nav item hover state */
.nav-item.hovered .nav-item-blob {
    opacity: 1;
    transform: scale(1);
}