<nav id="floating-navbar"
    class="fixed top-4 left-1/2 transform -translate-x-1/2 z-50 transition-all duration-700 ease-out bg-white shadow-xl scale-100 hover:scale-[1.02] rounded-lg border border-white/30 hover:border-white/50 group overflow-hidden w-[90vw]"
    >
    <div class="relative flex items-center justify-between px-4 sm:px-2 py-1 sm:py-4">
        <!-- Logo -->
        <div class="flex items-center space-x-3">
            <div class="relative">
                <a class="sm:ml-4 flex items-center text-neutral-900 hover:text-neutral-900 focus:text-neutral-900 dark:text-neutral-200 dark:hover:text-neutral-400 dark:focus:text-neutral-400 lg:mb-0 lg:mt-0 w-fit"
                    href="#">
                    <img src="{{ asset('assets/hima.png') }}" class="h-6 sm:h-10" alt="Logo HIMA"
                        loading="lazy" />
                </a>
            </div>
        </div>

        <!-- Desktop Navigation -->
        <div id="desktop-nav" class="hidden md:flex items-center space-x-12">
            <a href="#features"
                class="nav-item relative text-black hover:text-orange-500 font-medium transition-all duration-500 hover:scale-105 group/item">
                <span class="font-paytone relative z-10">Features</span>
                <span
                    class="absolute -bottom-2 left-0 w-0 h-0.5 bg-gradient-to-r from-orange-500 to-pink-500 transition-all duration-500 group-hover/item:w-full"></span>
                <div
                    class="nav-item-blob absolute -inset-3 bg-gradient-to-r from-orange-500/10 to-pink-500/10 rounded-full blur-sm transition-all duration-500 opacity-0 scale-75">
                </div>
            </a>
            <a href="#developer"
                class="nav-item relative text-black hover:text-orange-500 font-medium transition-all duration-500 hover:scale-105 group/item">
                <span class="font-paytone relative z-10">Developer</span>
                <span
                    class="absolute -bottom-2 left-0 w-0 h-0.5 bg-gradient-to-r from-orange-500 to-pink-500 transition-all duration-500 group-hover/item:w-full"></span>
                <div
                    class="nav-item-blob absolute -inset-3 bg-gradient-to-r from-orange-500/10 to-pink-500/10 rounded-full blur-sm transition-all duration-500 opacity-0 scale-75">
                </div>
            </a>
            <a href="#crypto"
                class="nav-item relative text-gray-700 hover:text-orange-500 font-medium transition-all duration-500 hover:scale-105 group/item">
                <span class="font-paytone relative z-10">Cryptocurrencies</span>
                <span
                    class="absolute -bottom-2 left-0 w-0 h-0.5 bg-gradient-to-r from-orange-500 to-pink-500 transition-all duration-500 group-hover/item:w-full"></span>
                <div
                    class="nav-item-blob absolute -inset-3 bg-gradient-to-r from-orange-500/10 to-pink-500/10 rounded-full blur-sm transition-all duration-500 opacity-0 scale-75">
                </div>
            </a>
        </div>

        <!-- Right Side Actions -->
        <div class="flex items-center space-x-3">

            <a href="#"
                class="font-paytone hidden md:block relative bg-[var(--coral)] text-white px-8 py-2 rounded-lg font-medium hover:shadow-2xl hover:scale-105 transition-all duration-500 group/cta overflow-hidden">
                <span class="relative z-10">Login</span>
            </a >

            <!-- Animated Hamburger -->
            <div class="md:hidden relative">
                <button id="hamburger-button" class="relative w-8 h-8 flex items-center justify-center group"
                    aria-label="Toggle menu">
                    <div class="w-6 h-6 relative">
                        <span
                            class="hamburger-line top-line absolute block h-0.5 bg-gray-700 rounded-full transform transition-all duration-300 ease-in-out rotate-0 translate-y-0 w-6 top-1 group-hover:w-5"></span>
                        <span
                            class="hamburger-line middle-line absolute block h-0.5 bg-gray-700 rounded-full transform transition-all duration-300 ease-in-out opacity-100 scale-100 w-6 top-3 group-hover:w-4"></span>
                        <span
                            class="hamburger-line bottom-line absolute block h-0.5 bg-gray-700 rounded-full transform transition-all duration-300 ease-in-out rotate-0 translate-y-0 w-6 top-5 group-hover:w-5"></span>
                    </div>
                </button>
            </div>
        </div>
    </div>

    <!-- Mobile Menu -->
    <div id="mobile-menu"
        class="md:hidden absolute top-full left-0 right-0 mt-4 bg-white/95 backdrop-blur-2xl rounded-3xl shadow-2xl border border-white/30 overflow-hidden transition-all duration-500 opacity-0 -translate-y-4 scale-95 pointer-events-none">
        <div class="relative px-8 py-6 space-y-6">
            <a href="#features"
                class="mobile-nav-item block text-gray-700 hover:text-orange-500 font-medium transition-all duration-500 py-3 px-4 rounded-2xl hover:bg-gradient-to-r hover:from-orange-50 hover:to-pink-50 hover:scale-105 transform">Features</a>
            <a href="#developer"
                class="mobile-nav-item block text-gray-700 hover:text-orange-500 font-medium transition-all duration-500 py-3 px-4 rounded-2xl hover:bg-gradient-to-r hover:from-orange-50 hover:to-pink-50 hover:scale-105 transform">Developer</a>
            <a href="#crypto"
                class="mobile-nav-item block text-gray-700 hover:text-orange-500 font-medium transition-all duration-500 py-3 px-4 rounded-2xl hover:bg-gradient-to-r hover:from-orange-50 hover:to-pink-50 hover:scale-105 transform">Cryptocurrencies</a>

            <div class="pt-4 border-t border-gray-200/50">
                <button
                    class="mobile-nav-item w-full bg-gradient-to-r from-orange-500 to-pink-500 text-white px-8 py-4 rounded-2xl font-semibold hover:shadow-xl transition-all duration-500 hover:scale-105 relative overflow-hidden group/mobile-cta">
                    <span class="relative z-10">GET METAMASK</span>
                    <div
                        class="absolute inset-0 bg-gradient-to-r from-transparent via-white/20 to-transparent -translate-x-full group-hover/mobile-cta:translate-x-full transition-transform duration-700">
                    </div>
                </button>
            </div>
        </div>
    </div>
</nav>
