document.addEventListener('DOMContentLoaded', function () {

  // --- GET DOM ELEMENTS ---
  const navbar = document.getElementById('floating-navbar');
  const hamburgerButton = document.getElementById('hamburger-button');
  const mobileMenu = document.getElementById('mobile-menu');
  const desktopNavItems = document.querySelectorAll('#desktop-nav .nav-item');
  const mobileNavItems = document.querySelectorAll('#mobile-menu .mobile-nav-item');

  // --- NAVBAR SCROLL BEHAVIOR ---
  // Toggles a 'scrolled' class on the navbar when the user scrolls down
  const handleScroll = () => {
    if (window.scrollY > 50) {
      navbar.classList.add('scrolled');
    } else {
      navbar.classList.remove('scrolled');
    }
  };

  // Attach the scroll event listener
  window.addEventListener('scroll', handleScroll);


  // --- MOBILE MENU TOGGLE ---
  // Toggles the 'menu-open' class on the hamburger and the mobile menu itself
  const toggleMobileMenu = () => {
      // Toggle the 'X' animation for the hamburger button
      hamburgerButton.classList.toggle('menu-open');

      // Check if the menu is currently hidden by looking for the 'opacity-0' class
      const isMenuHidden = mobileMenu.classList.contains('opacity-0');

      if (isMenuHidden) {
          // --- OPEN THE MENU ---
          // Remove classes that hide the menu
          mobileMenu.classList.remove('opacity-0', '-translate-y-4', 'scale-95', 'pointer-events-none');
          // Add classes that show the menu
          mobileMenu.classList.add('opacity-100', 'translate-y-0', 'scale-100', 'pointer-events-auto');

          // Trigger staggered animation for nav items
          mobileNavItems.forEach((item, index) => {
              item.style.animation = `slideIn 0.6s ease-out ${index * 100}ms forwards`;
          });

      } else {
          // --- CLOSE THE MENU ---
          // Remove classes that show the menu
          mobileMenu.classList.remove('opacity-100', 'translate-y-0', 'scale-100', 'pointer-events-auto');
          // Add classes that hide the menu
          mobileMenu.classList.add('opacity-0', '-translate-y-4', 'scale-95', 'pointer-events-none');
          
          // Clear item animations
          mobileNavItems.forEach(item => {
              item.style.animation = '';
          });
      }
  };

  // Attach click listener to the hamburger button
  if (hamburgerButton) {
      hamburgerButton.addEventListener('click', toggleMobileMenu);
  }
  
  // Close mobile menu when a link is clicked
  mobileNavItems.forEach(item => {
      item.addEventListener('click', () => {
          if (mobileMenu.classList.contains('menu-open')) {
              toggleMobileMenu();
          }
      });
  });


  // --- DESKTOP NAVIGATION HOVER EFFECTS ---
  // Adds a 'hovered' class to the parent nav item to trigger CSS animations
  desktopNavItems.forEach(item => {
    item.addEventListener('mouseenter', () => {
      item.classList.add('hovered');
    });
    item.addEventListener('mouseleave', () => {
      item.classList.remove('hovered');
    });
  });

});